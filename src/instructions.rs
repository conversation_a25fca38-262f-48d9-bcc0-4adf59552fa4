use crate::pb::ibi_cash_amm::v1 as ibi_cash_amm;
use borsh::{BorshDeserialize, BorshSerialize};
use substreams::errors::Error;
use substreams_solana::pb::sf::solana::r#type::v1::{Block, CompiledInstruction};

// Instruction discriminators for IBI Cash AMM
const INIT_GLOBAL_DISCRIMINATOR: [u8; 8] = [175, 175, 109, 31, 13, 152, 155, 237];
const UPDATE_GLOBAL_DISCRIMINATOR: [u8; 8] = [229, 23, 203, 151, 122, 227, 173, 42];
const CREATE_CURVE_DISCRIMINATOR: [u8; 8] = [156, 71, 204, 156, 191, 47, 66, 192];
const SWAP_DISCRIMINATOR: [u8; 8] = [248, 198, 158, 145, 225, 117, 135, 200];
const MIGRATE_DISCRIMINATOR: [u8; 8] = [123, 134, 81, 0, 49, 68, 98, 98];
const WITHDRAW_DISCRIMINATOR: [u8; 8] = [183, 18, 70, 156, 148, 109, 161, 34];

#[derive(BorshDeserialize, BorshSerialize, Debug)]
pub struct InitGlobalArgs {
    pub initial_virtual_token_reserves: u64,
    pub initial_virtual_sol_reserves: u64,
    pub initial_real_token_reserves: u64,
    pub token_total_supply: u64,
    pub mint_decimals: u8,
    pub migrate_fee_amount: u64,
    pub migration_token_allocation: u64,
}

#[derive(BorshDeserialize, BorshSerialize, Debug)]
pub struct CreateCurveArgs {
    pub name: String,
    pub symbol: String,
    pub uri: String,
}

#[derive(BorshDeserialize, BorshSerialize, Debug)]
pub struct SwapArgs {
    pub amount: u64,
    pub direction: u8, // 0 = buy, 1 = sell
    pub max_slippage_bps: u16,
}

#[derive(BorshDeserialize, BorshSerialize, Debug)]
pub struct MigrateArgs {
    pub migration_fee: u64,
}

pub fn parse_instruction(
    instruction: &CompiledInstruction,
    block: &Block,
    instruction_index: u32,
) -> Result<ibi_cash_amm::Events, Error> {
    let mut events = ibi_cash_amm::Events::default();

    if instruction.data.len() < 8 {
        // return Err(Error::Unexpected("Instruction data too short".to_string()));
        return Err(Error::msg("Instruction data too short"));
    }

    let discriminator: [u8; 8] = instruction.data[0..8]
        .try_into()
        // .map_err(|_| Error::Unexpected("Failed to extract discriminator".to_string()))?;
        .map_err(|_| Error::msg("Failed to extract discriminator"))?;

    let block_time = block
        .block_time
        .as_ref()
        .map(|t| t.timestamp.to_string())
        .unwrap_or_default();
    let slot = block.slot.to_string();
    let transaction_signature = block
        .transactions()
        .nth(instruction_index as usize / 10) // Approximate transaction index
        // .and_then(|tx| tx.signatures.first())
        .and_then(|tx| tx.transaction.as_ref().and_then(|t| t.signatures.first()))
        .map(|sig| sig.clone())
        .unwrap_or_default();

    match discriminator {
        INIT_GLOBAL_DISCRIMINATOR => {
            let args: InitGlobalArgs = BorshDeserialize::try_from_slice(&instruction.data[8..])
                .map_err(|e| {
                    // Error::Unexpected(format!("Failed to deserialize InitGlobal: {}", e))
                    Error::msg(format!("Failed to deserialize InitGlobal: {}", e))
                })?;

            let global_config = create_global_config(
                &args,
                &instruction,
                &block_time,
                &slot,
                &transaction_signature,
            );
            events
                .global_config_events
                .push(ibi_cash_amm::GlobalConfigEvent {
                    global_config: Some(global_config),
                    event_type: "INIT_GLOBAL".to_string(),
                    block_time: block_time.clone(),
                    slot: slot.clone(),
                    transaction_signature: transaction_signature.clone(),
                });
        }
        CREATE_CURVE_DISCRIMINATOR => {
            let args: CreateCurveArgs = BorshDeserialize::try_from_slice(&instruction.data[8..])
                .map_err(|e| {
                    // Error::Unexpected(format!("Failed to deserialize CreateCurve: {}", e))
                    Error::msg(format!("Failed to deserialize CreateCurve: {}", e))
                })?;

            let (token, bonding_curve) = create_token_and_curve(
                &args,
                &instruction,
                &block_time,
                &slot,
                &transaction_signature,
            );

            events.token_events.push(ibi_cash_amm::TokenEvent {
                token: Some(token),
                event_type: "CREATE_TOKEN".to_string(),
                block_time: block_time.clone(),
                slot: slot.clone(),
                transaction_signature: transaction_signature.clone(),
            });

            events
                .bonding_curve_events
                .push(ibi_cash_amm::BondingCurveEvent {
                    bonding_curve: Some(bonding_curve),
                    event_type: "CREATE_CURVE".to_string(),
                    block_time: block_time.clone(),
                    slot: slot.clone(),
                    transaction_signature: transaction_signature.clone(),
                });
        }
        SWAP_DISCRIMINATOR => {
            let args: SwapArgs = BorshDeserialize::try_from_slice(&instruction.data[8..])
                // .map_err(|e| Error::Unexpected(format!("Failed to deserialize Swap: {}", e)))?;
                .map_err(|e| Error::msg(format!("Failed to deserialize Swap: {}", e)))?;

            let trade = create_trade(
                &args,
                &instruction,
                &block_time,
                &slot,
                &transaction_signature,
                instruction_index,
            );
            events.trade_events.push(ibi_cash_amm::TradeEvent {
                trade: Some(trade),
                event_type: "SWAP".to_string(),
                block_time: block_time.clone(),
                slot: slot.clone(),
                transaction_signature: transaction_signature.clone(),
            });
        }
        MIGRATE_DISCRIMINATOR => {
            let args: MigrateArgs = BorshDeserialize::try_from_slice(&instruction.data[8..])
                // .map_err(|e| Error::Unexpected(format!("Failed to deserialize Migrate: {}", e)))?;
                .map_err(|e| Error::msg(format!("Failed to deserialize Migrate: {}", e)))?;

            let migration = create_migration(
                &args,
                &instruction,
                &block_time,
                &slot,
                &transaction_signature,
            );
            events.migration_events.push(ibi_cash_amm::MigrationEvent {
                migration: Some(migration),
                event_type: "MIGRATE".to_string(),
                block_time: block_time.clone(),
                slot: slot.clone(),
                transaction_signature: transaction_signature.clone(),
            });
        }
        _ => {
            // Unknown instruction, skip
        }
    }

    Ok(events)
}

fn create_global_config(
    args: &InitGlobalArgs,
    instruction: &CompiledInstruction,
    block_time: &str,
    slot: &str,
    transaction_signature: &[u8],
) -> ibi_cash_amm::GlobalConfig {
    // Extract accounts - typically global config is the first account
    let global_config_pubkey = instruction
        .accounts
        .get(0)
        .map(|&idx| format!("account_{}", idx))
        .unwrap_or_default();

    ibi_cash_amm::GlobalConfig {
        id: global_config_pubkey,
        initialized: true,
        admin: instruction
            .accounts
            .get(1)
            .map(|&idx| vec![idx])
            .unwrap_or_default(),
        fee_receiver: instruction
            .accounts
            .get(2)
            .map(|&idx| vec![idx])
            .unwrap_or_default(),
        migration_authority: instruction
            .accounts
            .get(3)
            .map(|&idx| vec![idx])
            .unwrap_or_default(),
        migration_token_allocation: args.migration_token_allocation.to_string(),
        migrate_fee_amount: args.migrate_fee_amount.to_string(),
        initial_virtual_token_reserves: args.initial_virtual_token_reserves.to_string(),
        initial_virtual_sol_reserves: args.initial_virtual_sol_reserves.to_string(),
        initial_real_token_reserves: args.initial_real_token_reserves.to_string(),
        token_total_supply: args.token_total_supply.to_string(),
        mint_decimals: args.mint_decimals as i32,
        raydium: vec![], // Will be set later
        bump: 0,         // Extract from account data if needed
        created_at: block_time.to_string(),
        updated_at: block_time.to_string(),
        transaction_signature: transaction_signature.to_vec(),
    }
}

fn create_token_and_curve(
    args: &CreateCurveArgs,
    instruction: &CompiledInstruction,
    block_time: &str,
    slot: &str,
    transaction_signature: &[u8],
) -> (ibi_cash_amm::Token, ibi_cash_amm::BondingCurve) {
    // Extract relevant accounts
    let mint_pubkey = instruction
        .accounts
        .get(0)
        .map(|&idx| format!("mint_{}", idx))
        .unwrap_or_default();
    let bonding_curve_pubkey = instruction
        .accounts
        .get(1)
        .map(|&idx| format!("curve_{}", idx))
        .unwrap_or_default();
    let creator_pubkey = instruction
        .accounts
        .get(2)
        .map(|&idx| vec![idx])
        .unwrap_or_default();

    let token = ibi_cash_amm::Token {
        id: mint_pubkey.clone(),
        mint: mint_pubkey.as_bytes().to_vec(),
        name: args.name.clone(),
        symbol: args.symbol.clone(),
        uri: args.uri.clone(),
        decimals: 6,                                  // Default for most tokens
        total_supply: "****************".to_string(), // 1B tokens with 6 decimals
        creator: creator_pubkey.clone(),
        created_at: block_time.to_string(),
        bonding_curve_id: bonding_curve_pubkey.clone(),
        total_trades: "0".to_string(),
        total_volume_sol: "0".to_string(),
        total_volume_tokens: "0".to_string(),
        total_fees: "0".to_string(),
        current_price: "0".to_string(),
        market_cap: "0".to_string(),
        migrated: false,
        migrated_at: "0".to_string(),
        migration_transaction: vec![],
        raydium_pool_address: vec![],
        transaction_signature: transaction_signature.to_vec(),
    };

    let bonding_curve = ibi_cash_amm::BondingCurve {
        id: bonding_curve_pubkey,
        mint: mint_pubkey.as_bytes().to_vec(),
        token_id: mint_pubkey,
        creator: creator_pubkey,
        global_config_id: "global_config".to_string(), // Reference to global config
        initial_real_token_reserves: "800000000000000".to_string(), // 800M tokens
        virtual_sol_reserves: "30000000000".to_string(), // 30 SOL
        virtual_token_reserves: "1073000000000000".to_string(), // 1.073B tokens
        real_sol_reserves: "0".to_string(),
        real_token_reserves: "800000000000000".to_string(),
        token_total_supply: "****************".to_string(),
        started: false,
        complete: false,
        progress_percentage: "0".to_string(),
        bump: 0,
        created_at: block_time.to_string(),
        updated_at: block_time.to_string(),
        transaction_signature: transaction_signature.to_vec(),
    };

    (token, bonding_curve)
}

fn create_trade(
    args: &SwapArgs,
    instruction: &CompiledInstruction,
    block_time: &str,
    slot: &str,
    transaction_signature: &[u8],
    instruction_index: u32,
) -> ibi_cash_amm::Trade {
    let trader_pubkey = instruction
        .accounts
        .get(0)
        .map(|&idx| vec![idx])
        .unwrap_or_default();
    let mint_pubkey = instruction
        .accounts
        .get(1)
        .map(|&idx| format!("mint_{}", idx))
        .unwrap_or_default();
    let bonding_curve_pubkey = instruction
        .accounts
        .get(2)
        .map(|&idx| format!("curve_{}", idx))
        .unwrap_or_default();

    let trade_type = if args.direction == 0 {
        ibi_cash_amm::TradeType::Buy
    } else {
        ibi_cash_amm::TradeType::Sell
    };

    let trade_id = format!(
        "{}_{}",
        hex::encode(transaction_signature),
        instruction_index
    );

    ibi_cash_amm::Trade {
        id: trade_id,
        token_id: mint_pubkey,
        bonding_curve_id: bonding_curve_pubkey,
        trader: trader_pubkey,
        trade_type: trade_type as i32,
        sol_amount: args.amount.to_string(),
        token_amount: "0".to_string(), // Will be calculated
        fee_amount: "0".to_string(),   // Will be calculated
        price_per_token: "0".to_string(),
        price_per_sol: "0".to_string(),
        virtual_sol_reserves_after: "0".to_string(),
        virtual_token_reserves_after: "0".to_string(),
        real_sol_reserves_after: "0".to_string(),
        real_token_reserves_after: "0".to_string(),
        bonding_curve_complete: false,
        progress_percentage_after: "0".to_string(),
        slot: slot.to_string(),
        block_time: block_time.to_string(),
        transaction_signature: transaction_signature.to_vec(),
        instruction_index: instruction_index as i32,
        created_at: block_time.to_string(),
    }
}

fn create_migration(
    args: &MigrateArgs,
    instruction: &CompiledInstruction,
    block_time: &str,
    slot: &str,
    transaction_signature: &[u8],
) -> ibi_cash_amm::Migration {
    let migration_id = hex::encode(transaction_signature);
    let token_pubkey = instruction
        .accounts
        .get(0)
        .map(|&idx| format!("mint_{}", idx))
        .unwrap_or_default();
    let bonding_curve_pubkey = instruction
        .accounts
        .get(1)
        .map(|&idx| format!("curve_{}", idx))
        .unwrap_or_default();
    let migration_authority = instruction
        .accounts
        .get(2)
        .map(|&idx| vec![idx])
        .unwrap_or_default();

    ibi_cash_amm::Migration {
        id: migration_id,
        token_id: token_pubkey,
        bonding_curve_id: bonding_curve_pubkey,
        migration_authority,
        migration_fee: args.migration_fee.to_string(),
        sol_liquidity: "0".to_string(),   // Will be calculated
        token_liquidity: "0".to_string(), // Will be calculated
        pool_address: vec![],
        amm_config: vec![],
        slot: slot.to_string(),
        block_time: block_time.to_string(),
        transaction_signature: transaction_signature.to_vec(),
        created_at: block_time.to_string(),
    }
}
